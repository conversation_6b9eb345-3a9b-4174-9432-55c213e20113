import type { Employee, EmployeeDetails, EmployeeKPI } from '../types'
import { db } from '../db'
import { supabaseAdminQuery } from '../supabase-admin'
import { supabase } from '../supabase'
import { getEmployeeAllDepartments } from '../utils'

/**
 * Universal manager role detection - checks if a person exists in managers table
 * @param employeeId The employee ID to check
 * @param employeeName The employee name (fallback for matching)
 * @returns Manager role if person is a manager, null otherwise
 */
export async function getManagerRole(employeeId: string, employeeName?: string): Promise<string | null> {
  try {
    const { supabaseAdmin } = await import('../supabase-admin')

    // First try to match by user_id (if employee ID matches manager user_id)
    const { data: managerByUserId } = await supabaseAdmin
      .from('appy_managers')
      .select('role')
      .eq('user_id', employeeId)
      .eq('active', true)
      .single()

    if (managerByUserId) {
      console.log(`🎯 [MANAGER ROLE] Found manager by user_id: ${employeeId} -> ${managerByUserId.role}`)
      return managerByUserId.role
    }

    // Fallback: try to match by name if provided
    if (employeeName) {
      const { data: managerByName } = await supabaseAdmin
        .from('appy_managers')
        .select('role')
        .eq('full_name', employeeName)
        .eq('active', true)
        .single()

      if (managerByName) {
        console.log(`🎯 [MANAGER ROLE] Found manager by name: ${employeeName} -> ${managerByName.role}`)
        return managerByName.role
      }
    }

    console.log(`👤 [MANAGER ROLE] No manager role found for: ${employeeName || employeeId}`)
    return null

  } catch (error) {
    console.error('❌ [ERROR] getManagerRole - Failed to check manager role:', error)
    return null
  }
}

export async function getEmployees(): Promise<Employee[]> {
  try {
    console.log('📋 [DEBUG] getEmployees - Starting to fetch all employees')
    const employees = await db.getEmployees()
    console.log('📋 [DEBUG] getEmployees - Fetched', employees.length, 'employees from database')
    
    const mappedEmployees = employees.map(emp => ({
      id: emp.id,
      fullName: emp.full_name,
      rate: emp.rate as 'hourly' | 'monthly', // DB rate field contains the compensation type
      departmentId: emp.department_id || '',
      departmentName: emp.department_name,
      managerId: emp.manager_id, // Legacy field - will be primary manager ID
      managerName: emp.manager_name, // Legacy field - will be primary manager name
      managerIds: emp.manager_ids || [], // New field - all manager IDs
      managers: (emp.managers || []).map(m => ({
        id: `${emp.id}-${m.managerId}`, // Generate stable ID for EmployeeManager type compatibility
        employeeId: emp.id,
        managerId: m.managerId,
        managerName: m.managerName,
        isPrimary: m.isPrimary,
        assignedAt: m.assignedAt,
        departmentId: m.departmentId,
        departmentName: m.departmentName
      })), // New field - full manager details with department info
      managerDepartments: (emp.managers || [])
        .map(m => m.departmentName)
        .filter(Boolean) as string[],
      allDepartments: getEmployeeAllDepartments({
        departmentName: emp.department_name,
        managers: emp.managers || []
      }),
      active: emp.active
    }))
    
    console.log('📋 [DEBUG] getEmployees - Returning', mappedEmployees.length, 'mapped employees')
    return mappedEmployees
  } catch (error) {
    console.error('❌ [ERROR] getEmployees - Failed to fetch employees from database:', error)
    // Fallback to empty array in case of database error
    return []
  }
}

export async function saveEmployee(emp: Partial<Employee>): Promise<Employee | null> {
  try {
    if (process.env.ENABLE_DEBUG_LOGS === 'true') {
      console.log('🔧 [DEBUG] saveEmployee called with:', {
        id: emp.id,
        fullName: emp.fullName,
        email: emp.email,
        isUpdate: !!emp.id
      })
    }

    if (emp.id) {
      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('🔄 [DEBUG] Updating existing employee with ID:', emp.id)
      }

      // Verify employee exists before updating
      const existingEmployee = await db.getEmployeeById(emp.id)
      if (!existingEmployee) {
        console.error('❌ [ERROR] Employee not found for update:', emp.id)
        throw new Error(`Employee with ID ${emp.id} not found`)
      }

      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('✅ [DEBUG] Employee found, proceeding with update')
      }

      // Split fullName into first and last name for consistency
      const nameParts = emp.fullName?.split(' ') || []
      const firstName = nameParts[0] || ''
      const lastName = nameParts.slice(1).join(' ') || ''

      const updatedEmployee = await db.updateEmployee(emp.id, {
        fullName: emp.fullName,
        firstName: firstName,
        lastName: lastName,
        email: emp.email,
        role: emp.role,
        bio: emp.bio,
        linkedinUrl: emp.linkedinUrl,
        twitterUrl: emp.twitterUrl,
        telegramUrl: emp.telegramUrl,
        compensation: 1000, // Default placeholder value for DB compatibility
        rate: emp.rate, // DB expects compensation type in rate field
        departmentId: emp.departmentId,
        managerId: emp.managerId || undefined,
        active: emp.active
      })

      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('✅ [DEBUG] Employee updated successfully')
      }

      // Map database response to Employee type
      const mappedEmployee: Employee = {
        id: updatedEmployee.id,
        fullName: updatedEmployee.full_name,
        firstName: updatedEmployee.first_name || undefined,
        lastName: updatedEmployee.last_name || undefined,
        email: updatedEmployee.email || undefined,
        bio: updatedEmployee.bio || undefined,
        linkedinUrl: updatedEmployee.linkedin_url || undefined,
        twitterUrl: updatedEmployee.twitter_url || undefined,
        telegramUrl: updatedEmployee.telegram_url || undefined,
        role: updatedEmployee.role || undefined,
        rate: updatedEmployee.rate as "hourly" | "monthly",
        departmentId: updatedEmployee.department_id || '',
        managerId: updatedEmployee.manager_id || undefined,
        active: updatedEmployee.active,
        updatedAt: updatedEmployee.updated_at || undefined
      }

      return mappedEmployee
    } else {
      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('➕ [DEBUG] Creating new employee')
      }

      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('🔧 [DEBUG] Creating employee with mapped fields:', {
          fullName: emp.fullName,
          email: emp.email,
          compensation: 1000, // Default placeholder value for DB compatibility
          rate: emp.rate, // DB expects compensation type in rate field
          departmentId: emp.departmentId,
          managerId: emp.managerId || undefined
        })
      }

      // Split fullName into first and last name for consistency
      const nameParts = emp.fullName?.split(' ') || []
      const firstName = nameParts[0] || ''
      const lastName = nameParts.slice(1).join(' ') || ''

      const createdEmployee = await db.createEmployee({
        fullName: emp.fullName!,
        firstName: firstName,
        lastName: lastName,
        email: emp.email!,
        role: emp.role,
        bio: emp.bio,
        linkedinUrl: emp.linkedinUrl,
        twitterUrl: emp.twitterUrl,
        telegramUrl: emp.telegramUrl,
        compensation: 1000, // Default placeholder value for DB compatibility
        rate: emp.rate!, // DB expects compensation type in rate field
        departmentId: emp.departmentId!,
        managerId: emp.managerId || undefined
      })

      if (process.env.ENABLE_DEBUG_LOGS === 'true') {
        console.log('✅ [DEBUG] Employee created successfully')
      }

      // Map database response to Employee type
      const mappedEmployee: Employee = {
        id: createdEmployee.id,
        fullName: createdEmployee.full_name,
        firstName: createdEmployee.first_name || undefined,
        lastName: createdEmployee.last_name || undefined,
        email: createdEmployee.email || undefined,
        bio: createdEmployee.bio || undefined,
        linkedinUrl: createdEmployee.linkedin_url || undefined,
        twitterUrl: createdEmployee.twitter_url || undefined,
        telegramUrl: createdEmployee.telegram_url || undefined,
        role: createdEmployee.role || undefined,
        rate: createdEmployee.rate as "hourly" | "monthly",
        departmentId: createdEmployee.department_id || '',
        managerId: createdEmployee.manager_id || undefined,
        active: createdEmployee.active,
        updatedAt: createdEmployee.updated_at || undefined
      }

      return mappedEmployee
    }
  } catch (error) {
    console.error('❌ [ERROR] Failed to save employee to database:', error)

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('unique constraint') && error.message.includes('email')) {
        throw new Error('An employee with this email address already exists')
      } else if (error.message.includes('foreign key')) {
        throw new Error('Invalid department or manager selected')
      } else {
        throw new Error(`Failed to save employee: ${error.message}`)
      }
    }

    throw new Error('Failed to save employee')
  }

  return null
}

export async function getEmployeeDetails(employeeId: string): Promise<EmployeeDetails | null> {
  try {
    const employee = await db.getEmployeeById(employeeId)
    if (!employee) return null

    return {
      id: employee.id,
      fullName: employee.full_name,
      departmentName: employee.department_name || 'N/A',
      rate: employee.rate as 'hourly' | 'monthly', // DB rate field contains compensation type
    }
  } catch (error) {
    console.error('Failed to fetch employee details:', error)
    return null
  }
}

/**
 * Get all employees under a manager's hierarchy (including sub-managers and their employees)
 * For super admins, returns ALL employees in the system
 * @param managerId The manager's user ID
 * @param userRole Optional user role - if 'super-admin', returns all employees
 * @returns Array of employees in the hierarchy with their hierarchy level
 */
export async function getEmployeesForManager(managerId: string, userRole?: string): Promise<Employee[]> {
  try {
    console.log('🔍 [EMPLOYEES DEBUG] getEmployeesForManager - Fetching employees for manager:', { managerId, userRole })

    // Special logging for CJN Automation and Bob
    if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
      console.log('🔍 [CJN EMPLOYEES DEBUG] Fetching employees for CJN Automation:', managerId)
    }
    if (managerId === 'user_2zgBSj0wjNqywixy4Btq5nsu5QU') {
      console.log('🔍 [BOB EMPLOYEES DEBUG] Fetching employees for Bob Wazneh:', managerId)
    }

    // For super admins, return ALL employees instead of hierarchical lookup
    if (userRole === 'super-admin') {
      console.log('🔥 [SUPER ADMIN] Fetching ALL employees for super admin:', managerId)
      
      const allEmployees = await getEmployees()
      console.log('🔥 [SUPER ADMIN] Found', allEmployees.length, 'total employees for super admin')
      
      // Special logging for super admins
      if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
        console.log('🔥 [CJN SUPER ADMIN] CJN Automation sees all employees:', {
          count: allEmployees.length,
          sample: allEmployees.slice(0, 3).map(e => ({ id: e.id, name: e.fullName, dept: e.departmentName }))
        })
      }
      if (managerId === 'user_2zgBSj0wjNqywixy4Btq5nsu5QU') {
        console.log('🔥 [BOB SUPER ADMIN] Bob Wazneh sees all employees:', {
          count: allEmployees.length,
          sample: allEmployees.slice(0, 3).map(e => ({ id: e.id, name: e.fullName, dept: e.departmentName }))
        })
      }
      
      return allEmployees
    }

    // For regular managers, use hierarchical lookup
    const { data, error } = await supabase.rpc('get_hierarchical_employees', {
      manager_user_id: managerId
    })

    if (error) {
      console.error('🚨 [ERROR] getEmployeesForManager - Supabase RPC error:', error)
      if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
        console.error('🚨 [CJN ERROR] CJN Automation RPC error:', error)
      }
      throw error
    }

    if (!data) {
      console.log('📝 [EMPLOYEES DEBUG] getEmployeesForManager - No data returned for manager:', managerId)
      if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
        console.log('📝 [CJN DEBUG] No data returned for CJN Automation')
      }
      return []
    }

    // Special logging for CJN Automation
    if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
      console.log('🔍 [CJN EMPLOYEES DEBUG] Raw data from RPC:', data)
    }

    console.log('✅ [DEBUG] getEmployeesForManager - Found', data.length, 'employees in hierarchy')
    console.log('📊 [DEBUG] getEmployeesForManager - Raw data sample:', data[0])
    console.log('📊 [DEBUG] getEmployeesForManager - Hierarchy breakdown:',
      data.reduce((acc: any, emp: any) => {
        acc[`Level ${emp.hierarchy_level}`] = (acc[`Level ${emp.hierarchy_level}`] || 0) + 1
        return acc
      }, {})
    )

    // Transform the data to match our Employee type
    const employees: Employee[] = data.map((emp: any) => ({
      id: emp.id,
      fullName: emp.full_name,
      rate: emp.rate as 'hourly' | 'monthly', // DB rate field contains the compensation type
      departmentId: emp.department_id || '',
      departmentName: emp.department_name || '',
      managerId: emp.manager_id,
      managerName: emp.manager_name || '',
      active: emp.active
    }))

    console.log('🎯 [EMPLOYEES DEBUG] getEmployeesForManager - Returning', employees.length, 'formatted employees')
    console.log('🎯 [EMPLOYEES DEBUG] getEmployeesForManager - Sample employee:', employees[0])

    // Special logging for CJN Automation
    if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
      console.log('🎯 [CJN EMPLOYEES DEBUG] Final result for CJN Automation:', {
        count: employees.length,
        employees: employees.map(e => ({ id: e.id, name: e.fullName, managerId: e.managerId }))
      })
    }

    return employees

  } catch (error) {
    console.error('🚨 [ERROR] getEmployeesForManager - Failed to fetch hierarchical employees:', error)

    // Special logging for CJN Automation
    if (managerId === 'user_2zb7hqgjUHn7tmpbVSgnmeWM167') {
      console.error('🚨 [CJN ERROR] Failed to fetch employees for CJN Automation:', error)
    }

    // Fallback to empty array in case of error
    return []
  }
}

/**
 * Get employee profile with all details
 * @param employeeId The employee ID
 * @returns Employee with profile fields or null
 */
export async function getEmployeeProfile(employeeId: string): Promise<Employee | null> {
  try {
    // Use the database function that now includes multi-manager support
    const employeeWithDetails = await db.getEmployeeById(employeeId)
    
    if (!employeeWithDetails) return null

    // Transform manager data with department information
    const managers = (employeeWithDetails.managers || []).map(m => ({
      id: `${employeeWithDetails.id}-${m.managerId}`,
      employeeId: employeeWithDetails.id,
      managerId: m.managerId,
      managerName: m.managerName,
      isPrimary: m.isPrimary,
      assignedAt: m.assignedAt,
      departmentId: m.departmentId,
      departmentName: m.departmentName
    }))

    // Build employee data with helper fields for department information
    const employeeData = {
      id: employeeWithDetails.id,
      fullName: employeeWithDetails.full_name,
      firstName: employeeWithDetails.first_name || undefined,
      lastName: employeeWithDetails.last_name || undefined,
      email: employeeWithDetails.email || undefined,
      bio: employeeWithDetails.bio || undefined,
      role: employeeWithDetails.role || undefined,
      linkedinUrl: employeeWithDetails.linkedin_url || undefined,
      twitterUrl: employeeWithDetails.twitter_url || undefined,
      telegramUrl: employeeWithDetails.telegram_url || undefined,
      rate: employeeWithDetails.rate,
      departmentId: employeeWithDetails.department_id || '',
      departmentName: employeeWithDetails.department_name,
      managerId: employeeWithDetails.manager_id, // Legacy field - primary manager
      managerName: employeeWithDetails.manager_name, // Legacy field - primary manager name
      managerIds: employeeWithDetails.manager_ids || [], // New field - all manager IDs
      managers: managers, // New field - full manager details with department info
      managerDepartments: managers
        .map(m => m.departmentName)
        .filter(Boolean) as string[],
      allDepartments: getEmployeeAllDepartments({
        departmentName: employeeWithDetails.department_name,
        managers: managers
      }),
      active: employeeWithDetails.active,
      updatedAt: employeeWithDetails.updated_at || undefined
    }

    return employeeData
  } catch (error) {
    console.error('Failed to fetch employee profile:', error)
    return null
  }
}

/**
 * Get employee KPIs
 * @param employeeId The employee ID
 * @returns Array of KPIs
 */
export async function getEmployeeKPIs(employeeId: string): Promise<EmployeeKPI[]> {
  try {
    const { data, error } = await supabaseAdminQuery
      .employeeKpis()
      .select('*')
      .eq('employee_id', employeeId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching employee KPIs:', error)
      return []
    }

    return data.map(kpi => ({
      id: kpi.id,
      employeeId: kpi.employee_id,
      kpiName: kpi.kpi_name,
      kpiValue: kpi.kpi_value || undefined,
      kpiTarget: kpi.kpi_target || undefined,
      kpiUnit: kpi.kpi_unit || undefined,
      period: kpi.period || undefined,
      description: kpi.description || undefined,
      createdAt: kpi.created_at,
      updatedAt: kpi.updated_at
    }))
  } catch (error) {
    console.error('Failed to fetch employee KPIs:', error)
    return []
  }
}