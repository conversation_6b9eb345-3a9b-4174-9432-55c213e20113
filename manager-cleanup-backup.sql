-- MANAGER TABLE CLEANUP BACKUP QUERIES
-- Generated: 2025-07-31
-- CRITICAL: These queries can restore the original state if something goes wrong

-- ============================================================================
-- BACKUP: Current state before any changes
-- ============================================================================

-- BACKUP: <PERSON>'s employees (12 employees on jessica_manager_id)
-- Current employees assigned to jessica_manager_id:
-- <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, 
-- <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, 
-- <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>

-- BACKUP: <PERSON>'s employees (2 employees on mario_manager_id)
-- Current employees assigned to ma<PERSON>_manager_id:
-- Burak Ozturk, Nina Katharina Laue

-- ============================================================================
-- RESTORE QUERIES (USE ONLY IF ROLLBACK NEEDED)
-- ============================================================================

-- RESTORE: Jessica Hasson's employees back to jessica_manager_id
-- UPDATE appy_employees 
-- SET manager_id = 'jessica_manager_id' 
-- WHERE full_name IN (
--     'Cameron Penkauskas', 'Daniel Khon-Perez', 'Davi Nonato Braid', 
--     'Edwin DavidBeaty Marlowe', 'Ian Stuart Povey', 'Javaid Ahmad Shah', 
--     'Mhedi Banafshei Kohneh Ferod', 'Oliver Clifford Anderson', 
--     'Panagiota Fragkaki', 'Claudio Umberto Antonio Aresu', 
--     'Candela Sol Silva', 'Sergio Velasco Huertas'
-- );

-- RESTORE: Jessica Hasson's employee-manager relationships back to jessica_manager_id
-- UPDATE appy_employee_managers 
-- SET manager_id = 'jessica_manager_id' 
-- WHERE employee_id IN (
--     SELECT id FROM appy_employees WHERE full_name IN (
--         'Cameron Penkauskas', 'Daniel Khon-Perez', 'Davi Nonato Braid', 
--         'Edwin DavidBeaty Marlowe', 'Ian Stuart Povey', 'Javaid Ahmad Shah', 
--         'Mhedi Banafshei Kohneh Ferod', 'Oliver Clifford Anderson', 
--         'Panagiota Fragkaki', 'Claudio Umberto Antonio Aresu', 
--         'Candela Sol Silva', 'Sergio Velasco Huertas'
--     )
-- );

-- RESTORE: Mario Nawfal's employees back to mario_manager_id
-- UPDATE appy_employees 
-- SET manager_id = 'mario_manager_id' 
-- WHERE full_name IN ('Burak Ozturk', 'Nina Katharina Laue');

-- RESTORE: Mario Nawfal's employee-manager relationships back to mario_manager_id
-- UPDATE appy_employee_managers 
-- SET manager_id = 'mario_manager_id' 
-- WHERE employee_id IN (
--     SELECT id FROM appy_employees WHERE full_name IN ('Burak Ozturk', 'Nina Katharina Laue')
-- );

-- RESTORE: Inactive manager records (if accidentally deleted)
-- INSERT INTO appy_managers (user_id, full_name, email, role, active, manager_id, department_id, created_at)
-- VALUES 
-- ('user_0xtcn9p1r_1753955180599', 'Bobby Zalke', '<EMAIL>', 'manager', false, null, '13edbbd7-607d-4f9b-b1b5-dc52da0a86a8', '2025-07-31 09:46:20.715359+00'),
-- ('user_eperc7jfu_1753955278808', 'John Cloude Chamandi', '<EMAIL>', 'manager', false, null, '13edbbd7-607d-4f9b-b1b5-dc52da0a86a8', '2025-07-31 09:47:58.889981+00');

-- ============================================================================
-- CRITICAL ISSUES IDENTIFIED
-- ============================================================================

-- ISSUE 1: Jessica Hasson (<EMAIL>) 
-- - Only has legacy ID: jessica_manager_id
-- - NO proper Clerk ID found
-- - Has 12 employees assigned
-- - CANNOT migrate without creating proper Clerk ID first

-- ISSUE 2: Mario Nawfal (<EMAIL>)
-- - Only has legacy ID: mario_manager_id  
-- - NO proper Clerk ID found
-- - Has 2 employees assigned
-- - CANNOT migrate without creating proper Clerk ID first

-- ISSUE 3: Hannah Hughes duplicate
-- - user_30dflARudARiIpm4D9Fdrm3PtHD (<EMAIL>)
-- - user_30dfoCvFtrPA5Kw3tMswUWLGV1j (<EMAIL>)
-- - Need to determine if same person or different people

-- ============================================================================
-- SAFE TO DELETE (NO DEPENDENCIES)
-- ============================================================================

-- Bobby Zalke inactive: user_0xtcn9p1r_1753955180599 (0 employees, 0 relationships)
-- John Chamandi inactive: user_eperc7jfu_1753955278808 (0 employees, 0 relationships)

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Check employee counts after migration:
-- SELECT manager_id, COUNT(*) FROM appy_employees GROUP BY manager_id ORDER BY COUNT(*) DESC;

-- Check for orphaned employees:
-- SELECT * FROM appy_employees WHERE manager_id NOT IN (SELECT user_id FROM appy_managers);

-- Check for orphaned employee-manager relationships:
-- SELECT * FROM appy_employee_managers WHERE manager_id NOT IN (SELECT user_id FROM appy_managers);
